#Requires -Version 5.1
<#
.SYNOPSIS
    Starts the simplified JoMaDe application.
.DESCRIPTION
    Simple script to start backend and frontend servers.
#>

Clear-Host
Write-Host "=== Starting JoMaDe Application (Simplified) ===" -ForegroundColor Cyan

# Configuration
$projectRoot = $PSScriptRoot
$backendDir = Join-Path $projectRoot "backend"
$frontendDir = Join-Path $projectRoot "frontend"
$venvPath = Join-Path $backendDir ".venv"

# === COMPREHENSIVE CLEANUP ===
Write-Host "`n--- Comprehensive Process Cleanup ---" -ForegroundColor Magenta

# Stop processes on our target ports
Write-Host "Cleaning up processes on target ports..." -ForegroundColor Yellow
Stop-ProcessOnPort -Port 8000  # Backend
Stop-ProcessOnPort -Port 3000  # Frontend

# Stop common development server processes that might interfere
Write-Host "Cleaning up common development processes..." -ForegroundColor Yellow
Stop-ProcessByName -ProcessName "uvicorn"
Stop-ProcessByName -ProcessName "python"  # This might be aggressive, but ensures clean start
Stop-ProcessByName -ProcessName "node"
Stop-ProcessByName -ProcessName "npm"

Write-Host "Process cleanup completed!" -ForegroundColor Green

# Enhanced function to stop all processes on ports
function Stop-ProcessOnPort {
    param ([int]$Port)
    try {
        # Get all connections on the port (not just the first one)
        $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        if ($connections) {
            $processIds = $connections | Select-Object -ExpandProperty OwningProcess | Sort-Object -Unique
            foreach ($processId in $processIds) {
                if ($processId -gt 0) {  # Valid process ID
                    Write-Host "Stopping process on port $Port (PID: $processId)..." -ForegroundColor Yellow
                    try {
                        Stop-Process -Id $processId -Force -ErrorAction Stop
                        Write-Host "Successfully stopped process $processId" -ForegroundColor Green
                    }
                    catch {
                        Write-Host "Failed to stop process $processId (may have already exited)" -ForegroundColor Yellow
                    }
                }
            }
            # Wait a bit longer for processes to fully terminate
            Start-Sleep -Seconds 3

            # Verify ports are free
            $remainingConnections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
            if ($remainingConnections) {
                Write-Host "Warning: Some processes may still be using port $Port" -ForegroundColor Yellow
            } else {
                Write-Host "Port $Port is now free" -ForegroundColor Green
            }
        } else {
            Write-Host "No processes found on port $Port" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "Error checking port $Port: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Function to kill processes by name (additional cleanup)
function Stop-ProcessByName {
    param ([string]$ProcessName)
    try {
        $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
        if ($processes) {
            Write-Host "Found $($processes.Count) $ProcessName process(es), stopping them..." -ForegroundColor Yellow
            foreach ($process in $processes) {
                try {
                    Stop-Process -Id $process.Id -Force -ErrorAction Stop
                    Write-Host "Stopped $ProcessName process (PID: $($process.Id))" -ForegroundColor Green
                }
                catch {
                    Write-Host "Failed to stop $ProcessName process (PID: $($process.Id))" -ForegroundColor Yellow
                }
            }
            Start-Sleep -Seconds 2
        }
    }
    catch {
        # Process name not found - this is normal
    }
}

# --- Backend Setup ---
Write-Host "`n--- Backend Setup ---" -ForegroundColor Magenta

# Check for Python virtual environment
$venvPythonPath = Join-Path (Join-Path $venvPath "Scripts") "python.exe"

if (-not (Test-Path $venvPath)) {
    Write-Host "Creating Python virtual environment..." -ForegroundColor Yellow
    Push-Location $backendDir
    try {
        python -m venv .venv
        Write-Host "Virtual environment created." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to create virtual environment. Please ensure Python is installed."
        exit 1
    }
    finally {
        Pop-Location
    }
}

# Install dependencies from requirements.txt
$requirementsPath = Join-Path $backendDir "requirements.txt"
$firecrawlPath = Join-Path (Join-Path $venvPath "Lib") (Join-Path "site-packages" "firecrawl")

# Check if all dependencies are installed (check for firecrawl as it's the newest)
if (-not (Test-Path $firecrawlPath)) {
    Write-Host "Installing/updating backend dependencies..." -ForegroundColor Yellow
    Push-Location $backendDir
    try {
        if (Test-Path $requirementsPath) {
            & $venvPythonPath -m pip install -r requirements.txt --quiet
        } else {
            & $venvPythonPath -m pip install fastapi uvicorn python-dotenv firecrawl-py openai --quiet
        }
        Write-Host "Backend dependencies installed." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to install backend dependencies: $($_.Exception.Message)"
        exit 1
    }
    finally {
        Pop-Location
    }
} else {
    Write-Host "Backend dependencies already installed." -ForegroundColor Green
}

# Start Backend Server
Write-Host "Starting backend server..." -ForegroundColor Yellow

Push-Location $backendDir
try {
    $backendProcess = Start-Process -FilePath $venvPythonPath -ArgumentList "-m", "uvicorn", "api:app", "--reload", "--host", "0.0.0.0", "--port", "8000" -PassThru -WindowStyle Minimized
    Write-Host "Backend server started (PID: $($backendProcess.Id))" -ForegroundColor Green

    # Simple health check
    Start-Sleep -Seconds 3
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
        Write-Host "Backend server is healthy!" -ForegroundColor Green
    }
    catch {
        Write-Host "Backend health check failed, but server may still be starting..." -ForegroundColor Yellow
    }
}
catch {
    Write-Error "Failed to start backend server: $($_.Exception.Message)"
    exit 1
}
finally {
    Pop-Location
}

# --- Frontend Setup ---
Write-Host "`n--- Frontend Setup ---" -ForegroundColor Magenta

# Check if npm is available
try {
    $npmVersion = npm --version
    Write-Host "Using npm version: $npmVersion" -ForegroundColor Green
}
catch {
    Write-Error "npm not found. Please install Node.js and npm."
    exit 1
}

# Install minimal frontend dependencies
$nodeModulesPath = Join-Path $frontendDir "node_modules"
$packageJsonPath = Join-Path $frontendDir "package.json"

if (-not (Test-Path $nodeModulesPath)) {
    Write-Host "Installing minimal frontend dependencies..." -ForegroundColor Yellow

    Push-Location $frontendDir
    try {
        npm install --silent
        Write-Host "Frontend dependencies installed successfully." -ForegroundColor Green
    }
    catch {
        Write-Error "Failed to install frontend dependencies: $($_.Exception.Message)"
        exit 1
    }
    finally {
        Pop-Location
    }
} else {
    Write-Host "Frontend dependencies already installed." -ForegroundColor Green
}

# Start Frontend Server
Write-Host "Starting frontend server..." -ForegroundColor Yellow

Push-Location $frontendDir
try {
    Write-Host "Launching Next.js development server..." -ForegroundColor Gray
    $frontendProcess = Start-Process -FilePath "npm" -ArgumentList "run", "dev" -PassThru -WindowStyle Minimized
    Write-Host "Frontend process started (PID: $($frontendProcess.Id))" -ForegroundColor Green

    # Wait longer for Next.js to compile and start
    Write-Host "Waiting for Next.js to compile and start (this may take 15-30 seconds)..." -ForegroundColor Gray
    Start-Sleep -Seconds 15

    # Health check with retries
    $maxRetries = 3
    $healthCheckPassed = $false

    for ($i = 1; $i -le $maxRetries; $i++) {
        try {
            Write-Host "Health check attempt $i/$maxRetries..." -ForegroundColor Gray
            $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 10 -ErrorAction Stop
            Write-Host "Frontend server is healthy!" -ForegroundColor Green
            $healthCheckPassed = $true
            break
        }
        catch {
            Write-Host "Attempt $i failed: $($_.Exception.Message)" -ForegroundColor Yellow
            if ($i -lt $maxRetries) {
                Start-Sleep -Seconds 10
            }
        }
    }

    if (-not $healthCheckPassed) {
        Write-Host "Frontend health check failed after $maxRetries attempts." -ForegroundColor Yellow
        Write-Host "The server may still be starting. Check http://localhost:3000 manually." -ForegroundColor Yellow
    }
}
catch {
    Write-Error "Failed to start frontend server: $($_.Exception.Message)"
    exit 1
}
finally {
    Pop-Location
}

# === Application Status ===
Write-Host "`n=== Application Status ===" -ForegroundColor Magenta
Write-Host "[OK] Backend: http://localhost:8000/docs" -ForegroundColor Green
Write-Host "[OK] Frontend: http://localhost:3000" -ForegroundColor Green

Write-Host "`nOpening http://localhost:3000 in browser..." -ForegroundColor Yellow
try {
    Start-Process "http://localhost:3000"
}
catch {
    Write-Warning "Could not open browser automatically."
}

Write-Host "`nJoMaDe application is running!" -ForegroundColor Cyan
Write-Host "Both servers are running in minimized windows." -ForegroundColor Gray
Write-Host "Press Enter to stop all servers..." -ForegroundColor Yellow
Read-Host

# === Shutdown ===
Write-Host "`nStopping servers..." -ForegroundColor Yellow

if ($frontendProcess -and -not $frontendProcess.HasExited) {
    Stop-Process -Id $frontendProcess.Id -Force -ErrorAction SilentlyContinue
    Write-Host "Frontend server stopped." -ForegroundColor Green
}

if ($backendProcess -and -not $backendProcess.HasExited) {
    Stop-Process -Id $backendProcess.Id -Force -ErrorAction SilentlyContinue
    Write-Host "Backend server stopped." -ForegroundColor Green
}

Write-Host "JoMaDe application stopped." -ForegroundColor Cyan